<template>
  <div class="used-vehicle-inventory-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">入库日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">库存状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.stockStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in stockStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">车辆来源</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.vehicleSource"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in vehicleSourceOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">库存单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><Building /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.stockOrgs && filterForm.stockOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的机构标签 -->
            <div
              v-if="filterForm.stockOrgs && filterForm.stockOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.stockOrgs"
                :key="org.id"
                type="success"
                size="small"
                closable
                @close="removeOrg(org)"
                style="margin-left: 4px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增库存
        </n-button>

        <file-upload-button
          button-type="info"
          button-text="Excel导入"
          button-mode="standard"
          accept-formats=".xlsx,.xls"
          :max-size="10"
          template-url="/templates/used-vehicle-inventory-template.xlsx"
          :button-style="{ fontWeight: 'bold' }"
          @success="handleImportSuccess"
          @error="handleImportError"
        />

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入车牌号或VIN"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          virtual-scroll
          virtual-scroll-x
          :scroll-x="scrollX"
          :min-row-height="48"
          :height-for-row="() => 48"
          virtual-scroll-header
          :header-height="48"
          striped
          size="medium"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择库存单位"
      business-permission="can_stock_in"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />

    <!-- 新增/编辑弹窗 -->
    <n-modal
      v-model:show="dialogVisible"
      :mask-closable="false"
      preset="card"
      :title="dialogTitle"
      :style="{
        width: '100vw',
        height: '100vh',
        maxWidth: '100vw',
        maxHeight: '100vh',
        margin: '0',
        borderRadius: '0',
      }"
      :segmented="{ content: true, footer: true }"
      transform-origin="center"
    >
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="4" :x-gap="24" :y-gap="16" responsive="screen">
          <n-form-item-gi label="车牌号" path="vehicleId">
            <n-input
              v-model:value="form.vehicleId"
              placeholder="请输入车牌号"
            />
          </n-form-item-gi>
          <n-form-item-gi label="VIN" path="vin">
            <n-input v-model:value="form.vin" placeholder="请输入17位VIN" />
          </n-form-item-gi>
          <n-form-item-gi label="品牌" path="brand">
            <n-input
              v-model:value="form.brand"
              placeholder="请输入车辆品牌"
              maxlength="20"
              show-count
            />
          </n-form-item-gi>
          <n-form-item-gi label="车型系列" path="series">
            <n-input
              v-model:value="form.series"
              placeholder="请输入车型系列"
              maxlength="20"
              show-count
            />
          </n-form-item-gi>
          <n-form-item-gi label="车辆类型" path="vehicleType">
            <n-input
              v-model:value="form.vehicleType"
              placeholder="请输入车辆类型"
            />
          </n-form-item-gi>
          <n-form-item-gi label="颜色" path="color">
            <n-input v-model:value="form.color" placeholder="请输入车辆颜色" />
          </n-form-item-gi>
          <n-form-item-gi label="里程数(公里)" path="mileage">
            <n-input-number
              :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
              button-placement="both"
              v-model:value="form.mileage"
              placeholder="请输入里程数"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="入库金额(元)" path="inboundAmount">
            <n-input-number
              :min="amountInputConfig.min"
              :max="amountInputConfig.max"
              :step="amountInputConfig.step"
              v-model:value="form.inboundAmount"
              button-placement="both"
              placeholder="请输入入库金额"
              :precision="2"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="入库日期" path="inboundDate">
            <n-date-picker
              v-model:value="form.inboundDate"
              type="date"
              placeholder="请选择入库日期"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="首次登记日期" path="registrationDate">
            <n-date-picker
              v-model:value="form.registrationDate"
              type="date"
              placeholder="请选择首次登记日期"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="保险到期日期" path="insuranceDeadline">
            <n-date-picker
              v-model:value="form.insuranceDeadline"
              type="date"
              placeholder="请选择保险到期日期"
              style="width: 100%"
            />
          </n-form-item-gi>
          <n-form-item-gi label="年检到期日期" path="checkDeadline">
            <n-date-picker
              v-model:value="form.checkDeadline"
              type="date"
              placeholder="请选择年检到期日期"
              style="width: 100%"
            />
          </n-form-item-gi>
        </n-grid>

        <!-- 客户信息区域 -->
        <div class="section-container">
          <div class="section-title">
            <span class="title-text">客户信息</span>
            <n-button
              type="primary"
              size="small"
              @click="showCustomerSelector"
              class="title-button"
            >
              <template #icon>
                <n-icon>
                  <PersonOutline />
                </n-icon>
              </template>
              选择客户
            </n-button>
            <n-button
              v-if="form.customerInfo"
              type="error"
              size="small"
              ghost
              @click="clearCustomer"
              class="title-button"
              style="margin-left: 8px"
            >
              <template #icon>
                <n-icon>
                  <CloseOutline />
                </n-icon>
              </template>
              清空
            </n-button>
          </div>
          <n-divider
            class="section-divider"
            style="
              height: 2px;
              background-image: linear-gradient(
                to right,
                var(--primary-color, #18a058) 0%,
                rgba(24, 160, 88, 0.1) 100%
              );
              border: none;
            "
          ></n-divider>

          <n-grid :cols="4" :x-gap="16" :y-gap="6">
            <n-grid-item>
              <n-form-item label="客户名称" path="inboundCustomerId" required>
                <n-input
                  :value="form.customerInfo?.customerName || ''"
                  placeholder="请选择客户"
                  readonly
                  style="cursor: pointer"
                  @click="showCustomerSelector"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="联系电话">
                <n-input
                  :value="form.customerInfo?.mobile || ''"
                  placeholder="联系电话"
                  readonly
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="客户类型">
                <n-input
                  :value="
                    form.customerInfo?.customerType === 'individual'
                      ? '个人客户'
                      : form.customerInfo?.customerType === 'institutional'
                      ? '法人客户'
                      : ''
                  "
                  placeholder="客户类型"
                  readonly
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="所属单位">
                <n-input
                  :value="form.customerInfo?.ownerOrgName || ''"
                  placeholder="所属单位"
                  readonly
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </div>

        <n-form-item label="备注" path="remark" style="margin-top: 16px">
          <n-input
            v-model:value="form.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </n-form-item>
      </n-form>

      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 12px">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">保存</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 查看详情弹窗 -->
    <n-modal
      v-model:show="detailDialogVisible"
      preset="card"
      title="查看详情"
      :style="{ width: '800px', maxHeight: '90vh' }"
      :segmented="{ content: true }"
    >
      <div v-if="currentDetailId">
        <p>详情ID: {{ currentDetailId }}</p>
        <p>这里可以显示详细信息...</p>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <n-button @click="detailDialogVisible = false">关闭</n-button>
        </div>
      </template>
    </n-modal>

    <!-- 出库弹窗 -->
    <outbound-dialog
      v-model:visible="outboundDialogVisible"
      :inventory-data="currentOutboundData"
      @success="handleOutboundSuccess"
    />

    <!-- 客户选择器 -->
    <customer-selector
      v-model:visible="customerSelectorVisible"
      :initial-customer="form.customerInfo"
      @select="handleCustomerSelect"
      @cancel="handleCustomerSelectCancel"
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import useUsedVehicleInventory from "./UsedVehicleInventoryPage.js";

// 使用组合式函数获取所有页面逻辑
const {
  // 组件引用
  FileUploadButton,
  BizOrgSelector,
  OutboundDialog,
  CustomerSelector,

  // 图标
  SearchOutline,
  RefreshOutline,
  AddOutline,
  CreateOutline,
  SwapHorizontalOutline,
  CopyOutline,
  LogOutOutline,
  PersonOutline,
  CloseOutline,
  Building,

  // 响应式数据
  tableRef,
  formRef,
  loading,
  dialogVisible,
  dialogTitle,
  isEdit,
  stockStatusOptions,
  vehicleSourceOptions,
  leadsSourceOptions,
  filterForm,
  form,
  rules,
  inventoryData,
  pagination,
  windowHeight,
  detailDialogVisible,
  currentDetailId,
  showOrgSelector,
  outboundDialogVisible,
  currentOutboundData,
  customerSelectorVisible,

  // 计算属性
  tableMaxHeight,
  filteredData,
  columns,
  scrollX,
  selectedOrgText,
  customerDisplayText,
  amountInputConfig,

  // 日期相关
  dateRangeOptions,

  // 业务方法
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  showAddDialog,
  handleEdit,
  handleView,
  handleSave,
  handlePageChange,
  handlePageSizeChange,
  handleImportSuccess,
  handleImportError,
  handleChangeStatus,
  refreshData,

  // 机构选择器相关方法
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,

  // 客户选择器相关方法
  showCustomerSelector,
  handleCustomerSelect,
  handleCustomerSelectCancel,
  clearCustomer,

  // 出库相关方法
  handleOutbound,
  handleOutboundSuccess,

  // 生命周期方法
  initialize,
  cleanup,
} = useUsedVehicleInventory();

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
@use "./UsedVehicleInventoryPage.scss";
@use "@/styles/section-title.scss";

.customer-info {
  margin: 16px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

// 全屏弹窗样式优化
:deep(.n-modal) {
  .n-card {
    height: 100vh;
    display: flex;
    flex-direction: column;

    .n-card__content {
      flex: 1;
      overflow-y: auto;
      padding: 24px;
    }

    .n-card__footer {
      flex-shrink: 0;
      padding: 16px 24px;
      border-top: 1px solid #e9ecef;
    }
  }
}

// 响应式网格优化
:deep(.n-grid) {
  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  @media (max-width: 900px) {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  @media (max-width: 600px) {
    grid-template-columns: repeat(1, 1fr) !important;
  }
}
</style>
