import { h } from 'vue'
import { NIcon, NTag } from 'naive-ui'
import { orderStatusUtils, getDictLabel, getDictType, getDictColor } from '@/utils/dictUtils'
import SearchIcon from '@/components/icons/SearchIcon.vue'

/**
 * 创建订单列表表格列配置
 * @param {Object} icons - 图标对象
 * @param {Object} handlers - 事件处理函数对象
 * @returns {Array} 表格列配置数组
 */
export function createOrdersTableColumns(icons, handlers) {
  const {
    CopyOutlineIcon,
    SearchIconComponent,
    CreateOutlineIcon,
    CloseCircleOutlineIcon
  } = icons

  const {
    copyToClipboard,
    handleView,
    handleEdit,
    handleDepositEdit,
    handleStatusChange,
    handleCancelOrder
  } = handlers

  return [
    { type: 'selection', width: 50, fixed: 'left' },

    {
      title: '订单编号',
      key: 'orderSn',
      width: 250,
      fixed: 'left',
      render(row) {
        return h('div', {
          style: {
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer'
          },
          onClick: () => copyToClipboard(row.orderSn),
          title: '点击复制订单编号'
        }, [
          h(NIcon, {
            size: 16,
            color: 'var(--primary-color)',
            style: {
              opacity: 0.8,
              transition: 'opacity 0.2s'
            },
            onMouseover: (e) => {
              e.target.style.opacity = 1
            },
            onMouseout: (e) => {
              e.target.style.opacity = 0.8
            }
          }, { default: () => h(CopyOutlineIcon) }),
          h('span', {
            style: {
              marginLeft: '5px',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              letterSpacing: '0.5px',
              fontWeight: 600
            }
          }, row.orderSn),
        ])
      }
    },

    {
      title: '销售日期',
      key: 'dealDate',
      width: 200,
      render(row) {
        if (!row.dealDate) return '未设置';
        const date = new Date(row.dealDate);
        return date.toLocaleDateString('zh-CN');
      }
    },

    {
      title: '订单类型',
      key: 'orderType',
      width: 150,
      render(row) {
        const typeMap = {
          'deposit': { text: '定金订单', type: 'info', color: '#2080f0' },
          'normal': { text: '销售订单', type: 'success', color: '#18a058' }
        }

        // 默认为销售订单
        const type = typeMap[row.orderType] || typeMap['normal']

        return h(
          NTag,
          {
            type: type.type,
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => type.text }
        )
      }
    },

    {
      title: '订单状态',
      key: 'orderStatus',
      width: 150,
      render(row) {
        return h(
          NTag,
          {
            type: orderStatusUtils.getType(row.orderStatus),
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => orderStatusUtils.getLabel(row.orderStatus) }
        )
      }
    },

    {
      title: '销售单位',
      key: 'salesOrgName',
      width: 320
    },

    {
      title: '销售地类型',
      key: 'salesStoreType',
      width: 120,
      render(row) {
        if (!row.salesStoreType) return '-';

        return h(
          NTag,
          {
            type: getDictType('sales_store_type', row.salesStoreType),
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => getDictLabel('sales_store_type', row.salesStoreType) }
        )
      }
    },

    {
      title: '销售顾问',
      key: 'salesAgentName',
      width: 240
    },

    {
      title: '客户名称',
      key: 'customerName',
      width: 140
    },

    {
      title: '联系电话',
      key: 'mobile',
      width: 120,
      render(row) {
        // 手机号脱敏处理：隐藏中间四位，以****代替
        if (!row.mobile) return '';
        if (row.mobile.length === 11) {
          return row.mobile.substring(0, 3) + '****' + row.mobile.substring(7);
        }
        return row.mobile;
      }
    },

    {
      title: '品牌',
      key: 'brand',
      width: 150
    },
    {
      title: '车型',
      key: 'series',
      width: 200
    },
    {
      title: '配置',
      key: 'configName',
      width: 300
    },

    {
      title: '应收金额(元)',
      key: 'amount',
      width: 200,
      sorter: (a, b) => {
        // 根据订单类型获取对应的金额字段进行排序
        const amountA = a.orderType === 'deposit' ? (a.depositAmount || 0) : (a.dealAmount || 0);
        const amountB = b.orderType === 'deposit' ? (b.depositAmount || 0) : (b.dealAmount || 0);
        return amountA - amountB;
      },
      render(row) {
        // 根据订单类型显示不同的金额字段
        let amount = 0;
        if (row.orderType === 'deposit') {
          amount = row.depositAmount || 0;
        } else {
          amount = row.dealAmount || 0;
        }

        // 金额单位是分，需要转换为元
        const amountInYuan = amount / 100;
        // 使用toLocaleString格式化为千分位，保留2位小数
        const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
        return h('span', { style: { fontWeight: 'bold' } }, `¥${formattedAmount}`)
      }
    },

    {
      title: '收款状态',
      key: 'receptStatus',
      width: 120,
      render(row) {
        if (!row.receptStatus) return '-';

        return h(
          NTag,
          {
            type: getDictType('recept_status', row.receptStatus),
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => getDictLabel('recept_status', row.receptStatus) }
        )
      }
    },

    {
      title: '操作',
      key: 'actions',
      width: 160,
      fixed: 'right',
      align: 'center',
      render: (row) => {
        const actions = [];

        // 查看按钮 - 所有状态都可以查看
        actions.push(
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#18a058',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleView(row.id),
              title: '查看详情'
            },
            [h(SearchIcon, { size: 20, color: '#18a058' })]
          )
        );

        // 编辑按钮 - 只有待处理状态可以编辑
        if (row.orderStatus === 'pending') {
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => {
                  // 根据订单类型调用不同的编辑函数
                  if (row.orderType === 'deposit') {
                    handleDepositEdit(row.id);
                  } else {
                    handleEdit(row.id);
                  }
                },
                title: '编辑订单'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
            )
          );
        }

        // 取消按钮 - 定金订单类型 + 收款状态为已收款 + 定金可用时可以取消
        if (row.orderType === 'deposit'
          && row.receptStatus === 'YES'
          && row.depositUsable === 'YES') {
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#d03050',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleCancelOrder(row.id, row.orderSn),
                title: '取消订单'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CloseCircleOutlineIcon) })]
            )
          );
        }

        return h('div', {
          style: {
            display: 'flex',
            justifyContent: 'center',
            gap: '8px'
          }
        }, actions)
      }
    }
  ]
}
