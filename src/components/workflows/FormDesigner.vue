<template>
  <div class="form-designer-container">
    <div class="form-designer-toolbar">
      <n-space>
        <n-button @click="handleBack">
          <template #icon>
            <n-icon><Close /></n-icon>
          </template>
          取消
        </n-button>
        <n-button type="primary" @click="handleSave">
          <template #icon>
            <n-icon><Save /></n-icon>
          </template>
          保存
        </n-button>
        <n-button @click="handlePreview">
          <template #icon>
            <n-icon><Eye /></n-icon>
          </template>
          预览
        </n-button>
      </n-space>
    </div>

    <div class="form-designer-content">
      <div class="component-list">
        <h3>基础组件</h3>
        <div class="component-group">
          <n-button
            v-for="component in basicComponents"
            :key="component.type"
            draggable="true"
            @dragstart="dragStart($event, component)"
            @click="addComponent(component)"
            class="component-button"
          >
            <template #icon>
              <n-icon>
                <component :is="component.icon" />
              </n-icon>
            </template>
            {{ component.label }}
          </n-button>
        </div>
        <h3>高级组件</h3>
        <div class="component-group">
          <n-button
            v-for="component in advancedComponents"
            :key="component.type"
            draggable="true"
            @dragstart="dragStart($event, component)"
            @click="addComponent(component)"
            class="component-button"
          >
            <template #icon>
              <n-icon>
                <component :is="component.icon" />
              </n-icon>
            </template>
            {{ component.label }}
          </n-button>
        </div>
      </div>
      <div class="form-canvas">
        <h3>表单画布</h3>
        <div class="form-canvas-content">
          <div
            v-for="(field, index) in formFields"
            :key="field.id"
            :class="['form-field', { selected: field.selected }]"
            :style="{ width: field.props.width }"
            draggable="true"
            @dragstart="dragStartField($event, index)"
            @dragover.prevent
            @dragenter="dragEnter($event, index)"
            @dragleave="dragLeave($event)"
            @drop.stop="dropField($event, index)"
            @click="selectField(field)"
          >
            <div class="field-content">
              <div class="field-label">
                <span v-if="field.props.required" class="required-mark">*</span>
                {{ field.label }}
                <!-- 这里使用 field.label，它会随着属性设置中的更改而更新 -->
                <span v-if="field.description" class="field-description">{{
                  field.description
                }}</span>
              </div>
              <div class="field-preview">
                <template v-if="field.type === 'memberSelectorSingle'">
                  <member-selector
                    :mode="'single'"
                    :label="
                      field.label || field.props.placeholder || '请选择成员'
                    "
                  />
                </template>
                <template v-else-if="field.type === 'memberSelectorMultiple'">
                  <member-selector
                    :mode="'multiple'"
                    :label="
                      field.label || field.props.placeholder || '请选择成员'
                    "
                  />
                </template>
                <template v-else-if="field.type === 'number'">
                  <n-input-number
                    :min="amountInputConfig.min"
                    :max="amountInputConfig.max"
                    :step="amountInputConfig.step"
                    v-bind="{ ...field.props, format: undefined }"
                    button-placement="both"
                    :formatter="
                      field.props.format === 'currency'
                        ? (value) => `¥${value}`
                        : field.props.format === 'percent'
                        ? (value) => `${value}%`
                        : undefined
                    "
                    :parser="
                      field.props.format === 'currency' ||
                      field.props.format === 'percent'
                        ? (value) => value.replace(/[^-\d.]/g, '')
                        : undefined
                    "
                    disabled
                  />
                </template>
                <template v-else-if="field.type === 'radio'">
                  <n-radio-group
                    :value="field.props.options[0].value"
                    :layout="field.props.layout"
                  >
                    <n-radio-button
                      v-for="option in field.props.options"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </n-radio-button>
                  </n-radio-group>
                </template>
                <template v-else-if="field.type === 'checkbox'">
                  <n-checkbox-group :value="[]" :layout="field.props.layout">
                    <n-checkbox
                      v-for="option in field.props.options"
                      :key="option.value"
                      :value="option.value"
                      :label="option.label"
                    />
                  </n-checkbox-group>
                </template>
                <template
                  v-else-if="
                    field.type === 'departmentSelect' ||
                    field.type === 'departmentMultiSelect'
                  "
                >
                  <department-selector
                    :mode="
                      field.type === 'departmentSelect' ? 'single' : 'multiple'
                    "
                    :label="
                      field.label || field.props.placeholder || '请选择部门'
                    "
                    :disabled="true"
                    :width="field.props.width"
                    :model-value="field.type === 'departmentSelect' ? null : []"
                  />
                </template>
                <component
                  v-else
                  :is="getComponentByType(field.type)"
                  v-bind="field.props"
                  disabled
                />
              </div>
            </div>
            <div class="field-actions">
              <n-button
                quaternary
                circle
                size="small"
                @click.stop="copyField(field)"
              >
                <template #icon>
                  <n-icon>
                    <Copy />
                  </n-icon>
                </template>
              </n-button>
              <n-button
                quaternary
                circle
                size="small"
                @click.stop="deleteField(index)"
              >
                <template #icon>
                  <n-icon>
                    <Delete />
                  </n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
      </div>
      <div class="property-panel">
        <h3>字段属性</h3>
        <n-form v-if="selectedField" label-placement="top">
          <n-form-item label="字段标题">
            <n-input v-model:value="selectedField.label" />
          </n-form-item>
          <n-form-item label="描述信息">
            <n-input v-model:value="selectedField.description" />
          </n-form-item>
          <n-form-item label="提示文字">
            <n-input v-model:value="selectedField.props.placeholder" />
          </n-form-item>
          <n-form-item label="校验">
            <n-space align="center">
              <n-checkbox v-model:checked="selectedField.props.required"
                >必需</n-checkbox
              >
              <n-checkbox v-model:checked="selectedField.props.unique"
                >唯一</n-checkbox
              >
            </n-space>
          </n-form-item>
          <n-form-item label="字段权限">
            <n-radio-group
              v-model:value="selectedField.props.permission"
              class="permission-radio-group"
            >
              <n-radio-button value="visible">仅可见</n-radio-button>
              <n-radio-button value="editable">可编辑</n-radio-button>
            </n-radio-group>
          </n-form-item>
          <template v-if="selectedField.type === 'input'">
            <n-form-item label="格式">
              <n-select
                v-model:value="selectedField.props.format"
                :options="inputFormatOptions"
              />
            </n-form-item>
            <n-form-item label="最大文本长">
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="selectedField.props.maxLength"
              />
            </n-form-item>
          </template>
          <template v-if="selectedField.type === 'textarea'">
            <n-form-item label="最大文本长度">
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="selectedField.props.maxLength"
              />
            </n-form-item>
          </template>
          <template v-if="selectedField.type === 'number'">
            <n-form-item label="格式">
              <n-select
                v-model:value="selectedField.props.format"
                :options="numberFormatOptions"
              />
            </n-form-item>
            <n-form-item label="显示格式">
              <n-checkbox
                v-model:checked="selectedField.props.thousandSeparator"
              >
                使用千分位分隔符
              </n-checkbox>
            </n-form-item>
            <n-form-item label="保留小数位数">
              <n-input-number
                :min="amountInputConfig.min"
                :step="amountInputConfig.step"
                v-model:value="selectedField.props.precision"
                :max="10"
                button-placement="both"
              >
                <template #suffix>位</template>
              </n-input-number>
            </n-form-item>
            <n-form-item label="最小值">
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="selectedField.props.min"
                button-placement="both"
                :precision="selectedField.props.precision"
              >
                <template
                  #prefix
                  v-if="selectedField.props.format === 'currency'"
                  >¥</template
                >
                <template
                  #suffix
                  v-if="selectedField.props.format === 'percent'"
                  >%</template
                >
              </n-input-number>
            </n-form-item>
            <n-form-item label="最大值">
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="selectedField.props.max"
                button-placement="both"
                :precision="selectedField.props.precision"
              >
                <template
                  #prefix
                  v-if="selectedField.props.format === 'currency'"
                  >¥</template
                >
                <template
                  #suffix
                  v-if="selectedField.props.format === 'percent'"
                  >%</template
                >
              </n-input-number>
            </n-form-item>
            <n-form-item label="步长">
              <n-input-number
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="selectedField.props.step"
                :min="0.01"
                button-placement="both"
                :precision="selectedField.props.precision"
              >
                <template
                  #prefix
                  v-if="selectedField.props.format === 'currency'"
                  >¥</template
                >
                <template
                  #suffix
                  v-if="selectedField.props.format === 'percent'"
                  >%</template
                >
              </n-input-number>
            </n-form-item>
            <n-form-item label="默认值">
              <n-input-number
                :min="amountInputConfig.min"
                :max="amountInputConfig.max"
                :step="amountInputConfig.step"
                v-model:value="selectedField.props.defaultValue"
                :precision="selectedField.props.precision"
                button-placement="both"
              >
                <template
                  #prefix
                  v-if="selectedField.props.format === 'currency'"
                  >¥</template
                >
                <template
                  #suffix
                  v-if="selectedField.props.format === 'percent'"
                  >%</template
                >
              </n-input-number>
            </n-form-item>
          </template>
          <template v-if="selectedField.type === 'datetime'">
            <n-form-item label="类">
              <n-select
                v-model:value="selectedField.props.type"
                :options="datetimeTypeOptions"
              />
            </n-form-item>
          </template>
          <template v-if="selectedField.type === 'radio'">
            <n-form-item label="选项">
              <n-dynamic-input
                v-model:value="selectedField.props.options"
                :on-create="onCreateOption"
                @update:value="updatePreview"
              >
                <template #default="{ value }">
                  <n-input
                    v-model:value="value.label"
                    placeholder="选项文本"
                    @update:value="updatePreview"
                  />
                </template>
              </n-dynamic-input>
            </n-form-item>
            <n-form-item label="选项排列方式">
              <n-radio-group
                v-model:value="selectedField.props.layout"
                @update:value="updatePreview"
              >
                <n-radio-button value="vertical">纵向排</n-radio-button>
                <n-radio-button value="horizontal">横向排列</n-radio-button>
              </n-radio-group>
            </n-form-item>
          </template>
          <n-divider />
          <n-form-item label="字段宽度">
            <n-radio-group
              v-model:value="selectedField.props.width"
              class="width-radio-group"
            >
              <template
                v-if="
                  selectedField.type === 'memberSelectorSingle' ||
                  selectedField.type === 'memberSelectorMultiple'
                "
              >
                <n-radio-button value="66.67%">2/3</n-radio-button>
                <n-radio-button value="75%">3/4</n-radio-button>
                <n-radio-button value="100%">整行</n-radio-button>
              </template>
              <template v-else>
                <n-radio-button value="25%">1/4</n-radio-button>
                <n-radio-button value="33.33%">1/3</n-radio-button>
                <n-radio-button value="50%">1/2</n-radio-button>
                <n-radio-button value="66.67%">2/3</n-radio-button>
                <n-radio-button value="75%">3/4</n-radio-button>
                <n-radio-button value="100%">整行</n-radio-button>
              </template>
            </n-radio-group>
          </n-form-item>
        </n-form>
      </div>
    </div>

    <FormPreview
      v-if="showPreview"
      :form-fields="formFields"
      @close="showPreview = false"
    />
  </div>
</template>


<script setup>
import { ref, watch, onMounted, onBeforeUnmount, computed } from "vue";
import {
  NInput,
  NButton,
  useDialog,
  NIcon,
  NDatePicker,
  NRadioGroup,
  NCheckboxGroup,
  NSelect,
  NDivider,
  NTabs,
  NUpload,
  NInputNumber,
  NCheckbox,
  NRadioButton,
  NDynamicInput,
  NForm,
  NFormItem,
  NSpace,
} from "naive-ui";
import { useMessage } from "naive-ui";
import FormPreview from "@/components/workflows/FormPreview.vue";
import MemberSelector from "@/components/users/MemberSelector.vue"; // 使用 MemberSelectorPanel 作为基础组件
import DepartmentSelector from "@/components/users/DepartmentSelector.vue";
// Tabler Icons
import {
  Calendar,
  Select,
  Selector,
  User,
  Users,
  Trash as Delete,
} from "@vicons/tabler";

// Material Icons
import {
  AbcOutlined,
  AccountTreeOutlined,
  AccountTreeSharp,
  BackupTableFilled,
  AttachFileRound,
  GpsNotFixedOutlined,
  DrawFilled,
  SmartButtonFilled,
  ContentCopyFilled as Copy,
} from "@vicons/material";

// Carbon Icons
import {
  PageNumber,
  RadioButtonChecked,
  CheckboxChecked,
  Split,
  Image,
  RegionAnalysisVolume,
  Table,
  Magnify,
  Data1,
  Barcode,
  ApplicationMobile,
  CdCreateExchange,
} from "@vicons/carbon";

import { useRouter } from "vue-router";
import { Eye, Close, Save } from "@vicons/ionicons5";
import { getNumberInputConfig } from "@/config/inputConfig";

const props = defineProps({
  workflow: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["save", "cancel", "close"]);

const dialog = useDialog();
const message = useMessage();

const currentTab = ref("form");
const formFields = ref([]);
const selectedField = ref(null);
const isModified = ref(false);
const isClosing = ref(false);
const keyupListener = ref(null);
const dragIndex = ref(null);
const dragOverIndex = ref(null);
const showPreview = ref(false);

const router = useRouter();

// 金额输入框配置
const amountInputConfig = computed(() => {
  return getNumberInputConfig("amount");
});

const basicComponents = [
  { type: "input", label: "单行文本", icon: AbcOutlined },
  { type: "textarea", label: "多行文本", icon: AbcOutlined },
  { type: "number", label: "数字", icon: PageNumber },
  { type: "datetime", label: "日期时间", icon: Calendar },
  { type: "radio", label: "单选按钮组", icon: RadioButtonChecked },
  { type: "checkbox", label: "复选框组", icon: CheckboxChecked },
  { type: "select", label: "下拉框", icon: Select },
  { type: "multiSelect", label: "下拉复选框", icon: Selector },
  {
    type: "memberSelectorSingle",
    label: "成员选择",
    icon: User,
    props: {
      placeholder: "请选择成员",
      required: false,
      unique: false,
      permission: "editable",
      width: "100%",
      mode: "single",
    },
  },
  {
    type: "memberSelectorMultiple",
    label: "成员多选",
    icon: Users,
    props: {
      placeholder: "请选择成员",
      required: false,
      unique: false,
      permission: "editable",
      width: "100%",
      mode: "multiple",
    },
  },
  {
    type: "departmentSelect",
    label: "部门单选",
    icon: AccountTreeOutlined,
    props: {
      placeholder: "请选择部门",
      required: false,
      unique: false,
      permission: "editable",
      width: "75%",
      mode: "single",
    },
  },
  {
    type: "departmentMultiSelect",
    label: "部门多选",
    icon: AccountTreeSharp,
    props: {
      placeholder: "请选择多个部门",
      required: false,
      unique: false,
      permission: "editable",
      width: "75%",
      mode: "multiple",
    },
  },
  { type: "divider", label: "分割线", icon: Split },
  { type: "tabs", label: "多标签页", icon: BackupTableFilled },
];

const advancedComponents = [
  { type: "image", label: "图片", icon: Image },
  { type: "attachment", label: "附件", icon: AttachFileRound },
  { type: "address", label: "地址", icon: RegionAnalysisVolume },
  { type: "location", label: "定位", icon: GpsNotFixedOutlined },
  { type: "subform", label: "子表单", icon: Table },
  { type: "query", label: "查询", icon: Magnify },
  { type: "dataSelect", label: "选择数据", icon: Data1 },
  { type: "signature", label: "手写签名", icon: DrawFilled },
  { type: "serialNumber", label: "流水号", icon: Barcode },
  { type: "phone", label: "手机号", icon: ApplicationMobile },
  { type: "ocr", label: "文字识别", icon: CdCreateExchange },
  { type: "button", label: "按钮", icon: SmartButtonFilled },
];

const inputFormatOptions = [
  { label: "无", value: "none" },
  { label: "手机号", value: "mobile" },
  { label: "电话号码", value: "phone" },
  { label: "邮政编码", value: "postcode" },
  { label: "身份证号码", value: "idcard" },
  { label: "邮箱地址", value: "email" },
];

const numberFormatOptions = [
  { label: "数值", value: "number" },
  { label: "百分比", value: "percent" },
  { label: "货币", value: "currency" },
];

const datetimeTypeOptions = [
  { label: "年-月-日", value: "date" },
  { label: "年-月-日 时:分", value: "datetime" },
  { label: "年-月-日 时:分:秒", value: "datetime" },
];

const onCreateOption = () => {
  return {
    label: "",
    value: Date.now().toString(),
  };
};

// 监听修改
watch(
  formFields,
  () => {
    isModified.value = true;
  },
  { deep: true }
);

// 拖拽开始
const dragStart = (event, component) => {
  event.dataTransfer.setData("text/plain", JSON.stringify(component));
};

// 放置组件
const drop = (event) => {
  // 如果字段拖拽，不处理
  if (event.dataTransfer.getData("text/plain") === "field-drag") {
    return;
  }

  try {
    const componentData = JSON.parse(event.dataTransfer.getData("text/plain"));
    addComponent(componentData);
  } catch (error) {
    console.error("Error dropping component:", error);
  }
};

// 择字段
const selectField = (field) => {
  try {
    // 重置所有字段的选中状态
    formFields.value.forEach((f) => {
      f.selected = f.id === field.id;
    });

    // 如果是新字段（刚添加的），重置其属性为默认值
    if (field.isNew) {
      field.props = getDefaultProps(field.type);
      field.label = field.type === "radio" ? "单选按钮组" : field.label; // 根据类型设置默认标签
      field.description = ""; // 清空描述
      delete field.isNew; // 移除新字段标记
    }

    // 更选中的段
    selectedField.value = field;
  } catch (error) {
    console.error("Error selecting field:", error);
  }
};

// 保存表单或流程
const saveForm = () => {
  try {
    // 实现保存逻辑
    if (currentTab.value === "form") {
      // 保存表单逻辑
      console.log("保存表单", formFields.value);
    } else if (currentTab.value === "process") {
      // 保存程逻辑
      console.log("保存流程");
    }
    isModified.value = false;
    emit("save", { tab: currentTab.value, data: formFields.value });
  } catch (error) {
    console.error("Error saving form:", error);
    throw error; // 重新抛出错，让调用者知道保存失败
  }
};

// 显示保存提示
const showSavePrompt = () => {
  return new Promise((resolve) => {
    dialog.warning({
      title: "警告",
      content: "您有未保存的更改，是否保存？我是弹窗内处理。",
      positiveText: "保存",
      negativeText: "放弃",
      closable: false,
      maskClosable: false,
      onPositiveClick: () => {
        saveForm();
        resolve(true);
      },
      onNegativeClick: () => {
        isModified.value = false;
        resolve(false);
      },
      onClose: () => {
        resolve(null);
      },
    });
  });
};

// 修改 closeEditor 方法
const closeEditor = async () => {
  if (isClosing.value) return false;
  isClosing.value = true;

  try {
    if (isModified.value) {
      const result = await showSavePrompt();
      if (result === null) {
        // 用户取消了操作
        return false;
      }
      // 如果用户择保存，result 为 true；如果选择不保存，result 为 false
      if (result) {
        try {
          await saveForm();
          message.success("保存成功");
        } catch (error) {
          message.error("保存失败");
          return false;
        }
      }
    }
    removeKeyupListener();
    emit("close", true); // 发送一个参数表示可以直接关闭
    return true;
  } finally {
    isClosing.value = false;
  }
};

// 修改 handleKeyUp 方法
const handleKeyUp = async (event) => {
  if (event.key === "Escape" && !isClosing.value) {
    event.preventDefault(); // 阻止认行为
    event.stopPropagation(); // 阻止事件冒泡
    await closeEditor();
  }
};

// 添加和移除事件监听器的方法
const addKeyupListener = () => {
  if (!keyupListener.value) {
    keyupListener.value = (event) => handleKeyUp(event);
    window.addEventListener("keyup", keyupListener.value);
  }
};

const removeKeyupListener = () => {
  if (keyupListener.value) {
    window.removeEventListener("keyup", keyupListener.value);
    keyupListener.value = null;
  }
};

// 修改 onMounted 和 onBeforeUnmount
onMounted(() => {
  addKeyupListener();
  window.addEventListener("beforeunload", handleBeforeUnload);
});

onBeforeUnmount(() => {
  removeKeyupListener();
  window.removeEventListener("beforeunload", handleBeforeUnload);
});

const handleBeforeUnload = (event) => {
  if (isModified.value) {
    event.preventDefault();
    event.returnValue = "";
  }
};

const getFormData = () => {
  // 返回当前表单的数据
  return {
    // ... 表单数据
  };
};

// 暴方法和属性给父组件
defineExpose({
  close: closeEditor,
  isModified,
  getFormData,
  showPreview,
  formFields,
});

// 添加一个新的函数来根���类型返回对应的组件
const getComponentByType = (type) => {
  const componentMap = {
    input: NInput,
    textarea: NInput,
    number: NInputNumber,
    datetime: NDatePicker,
    radio: NRadioGroup,
    checkbox: NCheckboxGroup,
    select: NSelect,
    multiSelect: NSelect,
    memberSelectorSingle: MemberSelector,
    memberSelectorMultiple: MemberSelector,
    departmentSelect: DepartmentSelector, // 添加部门单选
    departmentMultiSelect: DepartmentSelector, // 添加部门多选
  };
  return componentMap[type] || "div";
};

// 修改 getDefaultProps 方法
const getDefaultProps = (type) => {
  const commonProps = {
    placeholder: "",
    required: false,
    unique: false,
    permission: "editable",
    width: "100%",
  };

  const specificProps = {
    input: { format: "none", maxLength: 100 },
    textarea: { maxLength: 500 },
    number: {
      format: "number",
      precision: 0, // 默认不保留小数
      thousandSeparator: false,
      min: undefined, // 默认无最小值限制
      max: undefined, // 默认无大值限制
      step: 1, // 默认步长为1
      defaultValue: null, // 默认值为null
      placeholder: "请输入数字",
    },
    datetime: { type: "date" },
    radio: {
      options: [
        { label: "选项1", value: "1" },
        { label: "选项2", value: "2" },
      ],
      layout: "horizontal", // 默认横向排列
    },
    checkbox: {
      options: [
        { label: "选项1", value: "1" },
        { label: "选项2", value: "2" },
        { label: "选项3", value: "3" },
      ],
      layout: "horizontal", // 默认横向排列
      defaultValue: [], // 默认选中值为空数组
    },
    memberSelectorSingle: {
      mode: "single",
      placeholder: "请选一位成员",
      width: "75%",
    },
    memberSelectorMultiple: {
      mode: "multiple",
      placeholder: "请选择多位成员",
      width: "75%",
    },
    departmentSelect: {
      mode: "single",
      placeholder: "请选择部门",
      width: "75%",
    },
    departmentMultiSelect: {
      mode: "multiple",
      placeholder: "请选择多个部门",
      width: "75%",
    },
    // ... 其他组件型的默认属性
  };

  return { ...commonProps, ...(specificProps[type] || {}) };
};

// 修改 copyField 方法
const copyField = (field) => {
  try {
    const newField = JSON.parse(JSON.stringify(field));
    newField.id = Date.now();
    formFields.value.push(newField);
    isModified.value = true;
    selectField(newField); // 选中复制的字段
  } catch (error) {
    console.error("Error copying field:", error);
  }
};

// 修改 deleteField 方法
const deleteField = (index) => {
  try {
    formFields.value.splice(index, 1);
    isModified.value = true;
    if (
      selectedField.value &&
      selectedField.value.id === formFields.value[index]?.id
    ) {
      if (index > 0) {
        selectField(formFields.value[index - 1]); // 选中上一个字段
      } else if (formFields.value.length > 0) {
        selectField(formFields.value[0]); // 如果删除的是第一个，选中新的第一个
      } else {
        selectedField.value = null; // 如果没有字段了，清选择
      }
    }
  } catch (error) {
    console.error("Error deleting field:", error);
  }
};

// 修改 addComponent 方
const addComponent = (component) => {
  try {
    const newComponent = {
      ...component,
      id: Date.now(),
      label: component.label,
      props: getDefaultProps(component.type),
      isNew: true, // 添加新字段标记
    };
    formFields.value.push(newComponent);
    isModified.value = true;
    selectField(newComponent); // 选中新添加的组件
  } catch (error) {
    console.error("Error adding component:", error);
  }
};

// 修改 watch 以响应 props 的变化
watch(
  () => selectedField.value?.props,
  () => {
    if (selectedField.value) {
      const index = formFields.value.findIndex(
        (field) => field.id === selectedField.value.id
      );
      if (index !== -1) {
        formFields.value[index] = { ...selectedField.value };
      }
    }
  },
  { deep: true }
);

// 字段拖拽开始
const dragStartField = (event, index) => {
  dragIndex.value = index;
  event.dataTransfer.effectAllowed = "move";
  event.dataTransfer.setData("text/plain", "field-drag");
};

// 字段拖拽进入
const dragEnter = (event, index) => {
  if (dragIndex.value !== null && dragIndex.value !== index) {
    dragOverIndex.value = index;
    event.currentTarget.classList.add("drag-over");
  }
};

// 字段拖拽离开
const dragLeave = (event) => {
  event.currentTarget.classList.remove("drag-over");
};

// 字段放置
const dropField = (event, index) => {
  event.currentTarget.classList.remove("drag-over");

  // 查是否是字段拖拽
  if (event.dataTransfer.getData("text/plain") === "field-drag") {
    if (dragIndex.value !== null && dragIndex.value !== index) {
      // 移动字段位置
      const field = formFields.value[dragIndex.value];
      formFields.value.splice(dragIndex.value, 1);
      formFields.value.splice(index, 0, field);
      isModified.value = true;
    }
  }

  dragIndex.value = null;
  dragOverIndex.value = null;
};

// 添加一个新的方法用于同步更新预览区域
const updatePreview = () => {
  if (selectedField.value) {
    const index = formFields.value.findIndex(
      (field) => field.id === selectedField.value.id
    );
    if (index !== -1) {
      // 使用构和重新赋值来触发响应式更新
      formFields.value[index] = { ...selectedField.value };
    }
  }
};

// 添加工具栏按钮的处理方法
const handleBack = () => {
  emit("cancel");
};

const handleSave = () => {
  const formData = getFormData();
  emit("save", formData);
};

const handlePreview = () => {
  if (!formFields.value || formFields.value.length === 0) {
    message.warning("请先添加表单字段");
    return;
  }
  showPreview.value = true;
};
</script>

<style scoped>
/* 添加工具栏样式 */
.form-designer-toolbar {
  padding: 16px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
}

/* 修改容器样式 */
.form-designer-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 添加内容区域样式 */
.form-designer-content {
  flex: 1;
  display: flex;
  gap: 16px;
  overflow: hidden;
  position: relative;
  padding: 16px;
}

/* 容器样式 */
.form-designer-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 16px;
  overflow: hidden;
  position: relative; /* 添加相对定位 */
}

/* 组件列表面板 */
.component-list {
  width: 250px;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  bottom: 1px;
  left: 0;
  overflow-y: auto; /* 移到外层容器 */
}

.component-list > h3 {
  padding: 10px;
  margin: 0;
  flex-shrink: 0;
  position: sticky; /* 标题固定 */
  top: 0;
  background-color: #f9f9f9;
  z-index: 1;
}

.component-group {
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: none; /* 修改：不再使用 flex: 1 */
  overflow: visible; /* 修改：移除滚动 */
}

.component-button {
  width: calc(50% - 4px); /* 调整宽度以适应新的gap */
  text-align: left;
  padding: 6px 8px; /* 减小按钮内部的padding */
  height: 32px; /* 固定按钮高度 */
  display: flex;
  align-items: center;
}

.component-button :deep(.n-button__content) {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.component-button .n-icon {
  margin-right: 6px; /* 减小图标和文字的间距 */
  flex-shrink: 0;
}

/* 表单画布 */
.form-canvas {
  flex: 1;
  border: 1px solid #ccc;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: absolute; /* 使用绝对定位 */
  top: 0;
  bottom: 1px; /* 留出底部边框空间 */
  left: 266px; /* 250px + 16px gap */
  right: 296px; /* 280px + 16px gap */
}

.form-canvas > h3 {
  padding: 10px;
  margin: 0;
  flex-shrink: 0;
}

.form-canvas-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  min-height: 0; /* 确保内容可以滚动 */
}

.form-field {
  position: relative;
  padding: 10px;
  border: 1px solid #ddd;
  margin-bottom: 10px;
  cursor: move;
  box-sizing: border-box;
  transition: all 0.3s ease;
  background-color: #fff;
}

.form-field.selected {
  background-color: #e6f7e6;
}

.form-field.drag-over {
  border: 2px dashed #18a058;
  margin-top: 20px;
  position: relative;
}

.form-field.drag-over::before {
  content: "";
  position: absolute;
  top: -12px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #18a058;
}

.field-content {
  padding-right: 60px;
}

.field-label {
  margin-bottom: 5px;
  font-weight: bold;
}

.field-preview {
  pointer-events: none;
  opacity: 0.7;
}

.field-preview :deep(*) {
  cursor: default !important;
}

.field-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 5px;
  z-index: 1;
}

.field-description {
  font-weight: normal;
  color: #999;
  margin-left: 8px;
  font-size: 0.9em;
}

.required-mark {
  color: red;
  margin-right: 4px;
}

/* 属性面板 */
.property-panel {
  width: 280px;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  position: absolute; /* 使用绝对定位 */
  top: 0;
  bottom: 1px; /* 留出底部边框空间 */
  right: 0;
}

.property-panel > h3 {
  padding: 10px;
  margin: 0;
  flex-shrink: 0;
}

.property-panel > .n-form {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  min-height: 0;
}

/* 宽度选择器样式 */
:deep(.width-radio-group) {
  flex-wrap: wrap;
  gap: 4px !important;
  width: 100%;
}

:deep(.width-radio-group .n-radio-button) {
  min-width: 80px;
  flex: 0 0 calc(50% - 2px);
}

/* 权限选择器样式 */
:deep(.permission-radio-group) {
  width: 100%;
  display: flex;
  gap: 8px !important;
}

:deep(.permission-radio-group .n-radio-button) {
  flex: 1;
}

/* 调整表单项的间距 */
.property-panel :deep(.n-form-item) {
  margin-bottom: 12px;
}
</style>
