<template>
  <div v-if="sectionConfig.visible" class="section-container">
    <div class="section-title">
      <span class="title-text">出库信息</span>
      <div style="flex: 1"></div>
      <!-- 添加一个占位元素，将剩余空间推到右侧 -->
    </div>
    <n-divider
      class="section-divider"
      style="
        height: 2px;
        background-image: linear-gradient(
          to right,
          var(--primary-color, #18a058) 0%,
          rgba(24, 160, 88, 0.1) 100%
        );
        border: none;
      "
    ></n-divider>

    <!-- 出库信息表单 -->
    <n-grid :cols="4" :x-gap="16" :y-gap="1">
      <n-grid-item v-if="isFieldVisible('expectedOutboundDate')">
        <n-form-item label="预计出库日期" path="expectedOutboundDate" required>
          <n-date-picker
            v-model:value="form.expectedOutboundDate"
            type="date"
            clearable
            style="width: 100%"
            value-format="timestamp"
            @update:value="handleDateChange('expectedOutboundDate', $event)"
            :disabled="!isFieldEditable('expectedOutboundDate')"
          />
        </n-form-item>
      </n-grid-item>
      <n-grid-item v-if="isFieldVisible('outboundOrgName')">
        <n-form-item label="出库单位" path="outboundOrgName" required>
          <div
            class="outbound-org-selector"
            :class="{
              disabled: !form.skuId || !isFieldEditable('outboundOrgName'),
            }"
          >
            <n-input
              v-model:value="form.outboundOrgName"
              placeholder="请先选择车辆SKU"
              readonly
              @click="handleOutboundOrgClick"
            >
              <template #suffix>
                <n-button
                  v-if="isFieldEditable('outboundOrgName')"
                  quaternary
                  circle
                  @click.stop="handleOutboundOrgClick"
                  :disabled="!form.skuId"
                >
                  <template #icon>
                    <n-icon class="add-icon" :class="{ disabled: !form.skuId }">
                      <component :is="AddOutlineIcon" />
                    </n-icon>
                  </template>
                </n-button>
              </template>
            </n-input>
          </div>
        </n-form-item>
      </n-grid-item>
      <n-grid-item>
        <!-- 空白占位 -->
      </n-grid-item>
      <n-grid-item>
        <!-- 空白占位 -->
      </n-grid-item>
    </n-grid>

    <!-- 业务机构选择器 -->
    <BizOrgSelector
      :visible="showBizOrgSelector"
      title = "请选择出库单位"
      @update:visible="showBizOrgSelector = $event"
      :business-permission="'can_stock_out'"
      :initial-biz-org="selectedOutboundOrg"
      :single="true"
      :brand="form.vehicleBrand"
      :sku-id="form.skuId"
      :listAll="true"
      @select="handleOutboundOrgSelect"
      @cancel="handleOutboundOrgCancel"
    />
  </div>
</template>

<script setup>
import { computed, markRaw, ref } from "vue";
import { AddOutline } from "@vicons/ionicons5";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import messages from "@/utils/messages";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const AddOutlineIcon = markRaw(AddOutline);

// 定义组件属性
const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  selectedOutboundOrg: {
    type: Object,
    default: null,
  },
  config: {
    type: Object,
    default: () => ({
      visible: true,
      editable: true,
      fields: {},
    }),
  },
});

// 计算属性：获取section配置
const sectionConfig = computed(() => {
  return (
    props.config || {
      visible: true,
      editable: true,
      fields: {},
    }
  );
});

// 工具函数：检查字段是否可见
const isFieldVisible = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  return fieldConfig?.visible !== false; // 默认可见
};

// 工具函数：检查字段是否可编辑
const isFieldEditable = (fieldName) => {
  const fieldConfig = sectionConfig.value.fields?.[fieldName];
  const sectionEditable = sectionConfig.value.editable !== false;
  const fieldEditable = fieldConfig?.editable !== false;
  return sectionEditable && fieldEditable; // 区域和字段都必须可编辑
};

// 定义组件事件
const emit = defineEmits(["handle-outbound-org-change", "update:form"]);

// 计算属性：表单数据的引用
const form = computed(() => props.form);

// 组件状态
const showBizOrgSelector = ref(false);

// 处理日期变化
const handleDateChange = (field, value) => {
  const updatedForm = {
    ...props.form,
    [field]: value,
  };
  emit("update:form", updatedForm);
};

// 处理出库单位选择
const handleOutboundOrgSelect = (orgs) => {
  if (orgs && orgs.length > 0) {
    const selectedOrg = orgs[0]; // 因为是单选模式，所以取第一个

    // BizOrgSelector返回的字段是orgName，需要正确映射
    const updatedForm = {
      ...props.form,
      outboundOrgName: selectedOrg.orgName || selectedOrg.name || "",
      outboundOrgId: selectedOrg.id,
    };

    emit("update:form", updatedForm);
    emit("handle-outbound-org-change", selectedOrg);

    // 关闭选择器
    showBizOrgSelector.value = false;
  }
};

// 处理出库单位选择取消
const handleOutboundOrgCancel = () => {
  // 更新表单数据
  const updatedForm = {
    ...props.form,
    outboundOrgName: "",
    outboundOrgId: null,
  };

  emit("update:form", updatedForm);
  emit("handle-outbound-org-change", null);
  showBizOrgSelector.value = false;
};

// 处理出库单位点击
const handleOutboundOrgClick = () => {
  if (!isFieldEditable("outboundOrgName")) {
    return;
  }
  if (!props.form.skuId) {
    messages.warning("请先选择车辆SKU");
    return;
  }
  // 打开选择器前，先清空当前选择
  const updatedForm = {
    ...props.form,
    outboundOrgName: "",
    outboundOrgId: null,
  };
  emit("update:form", updatedForm);
  showBizOrgSelector.value = true;
};
</script>

<style lang="scss" scoped>
.outbound-org-selector {
  width: 100%;

  &.disabled {
    :deep(.n-input) {
      cursor: not-allowed;
      background-color: var(--n-disabled-color);
    }
  }

  :deep(.n-input) {
    width: 100%;
    cursor: pointer;
  }

  :deep(.n-button) {
    margin-right: -4px;

    &.n-button--disabled {
      cursor: not-allowed;
    }
  }

  .add-icon {
    color: var(--primary-color, #18a058);
    font-size: 18px;

    &.disabled {
      color: var(--n-disabled-color);
    }
  }
}
</style>
